import { Dialog, Transition } from "@headlessui/react";
import { Fragment, useState } from "react";
import { router } from "@inertiajs/react";
import { toast } from "react-toastify";
import setDefaultDateFormat from "@/Util/setDefaultDateFormat";
import SecondaryButton from "@/Components/SecondaryButton";
import DangerButton from "@/Components/DangerButton";

export default function CancelDomainDeletionModal({ isOpen, onClose, deletionRequest }) {
    const [submitting, setSubmitting] = useState(false);
    const [domainInput, setDomainInput] = useState("");
    const [supportNote, setSupportNote] = useState("");
    const [selectedReason, setSelectedReason] = useState("Others");
    const [isValid, setIsValid] = useState(false);

    const reasons = [
        "Technical Issues",
        "User Request",
        "Administrative Decision",
        "Others"
    ];

    const handleDomainInputChange = (e) => {
        const value = e.target.value;
        setDomainInput(value);
        setIsValid(value.trim().toLowerCase() === deletionRequest.domainName.toLowerCase() && supportNote.trim().length > 0);
    };

    const handleSupportNoteChange = (e) => {
        const value = e.target.value;
        setSupportNote(value);
        setIsValid(domainInput.trim().toLowerCase() === deletionRequest.domainName.toLowerCase() && value.trim().length > 0);
    };

    const handleReasonChange = (e) => {
        setSelectedReason(e.target.value);
    };

    const handleSubmit = () => {
        if (!isValid) return;

        setSubmitting(true);

        const finalSupportNote = selectedReason === "Others" ? supportNote : `${selectedReason}: ${supportNote}`;

        axios.post(
            route("domain.delete-request.cancel"),
            {
                domainName: deletionRequest.domainName,
                userEmail: deletionRequest.email,
                domainId: deletionRequest.domain_id,
                createdDate: deletionRequest.created_at,
                userID: deletionRequest.user_id,
                support_note: finalSupportNote,
            },
        )
        .then((response) => {
                toast.success("Domain deletion request cancelled successfully.");
                onClose();
                setSubmitting(false);
                router.visit(route("domain.delete-request.view"));
                return response;
            })
            .catch((error) => {
                setSubmitting(false);
                console.log(error.response);
                toast.error(error.response?.data?.message || "Failed to cancel deletion request.");
                return error.response;
            });
    };

    const handleClose = () => {
        setDomainInput("");
        setSupportNote("");
        setSelectedReason("Others");
        setIsValid(false);
        setSubmitting(false);
        onClose();
    };

    return (
        <Transition appear show={isOpen} as={Fragment}>
            <Dialog as="div" className="relative z-10" onClose={handleClose}>
                <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <div className="fixed inset-0 bg-black bg-opacity-25" />
                </Transition.Child>

                <div className="fixed inset-0 overflow-y-auto">
                    <div className="flex min-h-full items-center justify-center p-4 text-center">
                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-300"
                            enterFrom="opacity-0 scale-95"
                            enterTo="opacity-100 scale-100"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100 scale-100"
                            leaveTo="opacity-0 scale-95"
                        >
                            <Dialog.Panel className="w-full max-w-lg transform overflow-hidden rounded-lg bg-white p-6 text-left align-middle shadow-xl transition-all">
                                <Dialog.Title
                                    as="h3"
                                    className="text-lg font-medium leading-6 text-gray-900 mb-4"
                                >
                                    Cancel Domain: {deletionRequest.domainName}
                                </Dialog.Title>

                                <div className="mt-2">
                                    <p className="text-sm text-gray-600 mb-6">
                                        To cancel {deletionRequest.domainName}, please provide a detailed reason for our review.
                                    </p>

                                    {/* Domain Information */}
                                    <div className="bg-gray-50 p-4 rounded-lg mb-6">
                                        <div className="grid grid-cols-2 gap-4 text-sm">
                                            <div>
                                                <span className="font-medium text-gray-700">Client Name:</span>
                                                <div className="text-gray-900">{deletionRequest.first_name} {deletionRequest.last_name}</div>
                                            </div>
                                            <div>
                                                <span className="font-medium text-gray-700">Client Email:</span>
                                                <div className="text-gray-900">{deletionRequest.email}</div>
                                            </div>
                                            <div>
                                                <span className="font-medium text-gray-700">Client Domain:</span>
                                                <div className="text-gray-900">{deletionRequest.domainName}</div>
                                            </div>
                                            <div>
                                                <span className="font-medium text-gray-700">Request Date:</span>
                                                <div className="text-gray-900">{setDefaultDateFormat(deletionRequest.requested_at)}</div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Domain Confirmation Input */}
                                    <div className="mb-4">
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Type {deletionRequest.domainName} to confirm <span className="text-red-500">*</span>
                                        </label>
                                        <input
                                            type="text"
                                            value={domainInput}
                                            onChange={handleDomainInputChange}
                                            placeholder={`Type "${deletionRequest.domainName}" here`}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        />
                                    </div>

                                    {/* Reason Dropdown */}
                                    <div className="mb-4">
                                        {/* <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Reason for Cancellation <span className="text-red-500">*</span>
                                        </label> */}
                                        {/* <select
                                            value={selectedReason}
                                            onChange={handleReasonChange}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        >
                                            {reasons.map((reason) => (
                                                <option key={reason} value={reason}>
                                                    {reason}
                                                </option>
                                            ))}
                                        </select> */}
                                    </div>

                                    {/* Support Note Textarea */}
                                    <div className="mb-4">
                                        <textarea
                                            value={supportNote}
                                            onChange={handleSupportNoteChange}
                                            placeholder="Please provide your reason..."
                                            rows={4}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                                        />
                                        <div className="text-right text-xs text-gray-500 mt-1">
                                            {supportNote.length}/500 characters
                                        </div>
                                    </div>
                                </div>

                                <div className="mt-6 flex justify-end space-x-3">
                                    <SecondaryButton
                                        onClick={handleClose}
                                        disabled={submitting}
                                    >
                                        CANCEL
                                    </SecondaryButton>
                                    <DangerButton
                                        onClick={handleSubmit}
                                        disabled={!isValid || submitting}
                                        processing={submitting}
                                    >
                                        {submitting ? "CANCELLING..." : "CANCEL DOMAIN"}
                                    </DangerButton>
                                </div>

                            </Dialog.Panel>
                        </Transition.Child>
                    </div>
                </div>
            </Dialog>
        </Transition>
    );
}
