import { Dialog, Transition } from "@headlessui/react";
import { Fragment, useState } from "react";
import { router } from "@inertiajs/react";
import { toast } from "react-toastify";
import SecondaryButton from "@/Components/SecondaryButton";
import DangerButton from "@/Components/DangerButton";

export default function CreateDomainDeletionModal({ isOpen, onClose, domain }) {
    const [submitting, setSubmitting] = useState(false);
    const [inputValue, setInputValue] = useState("");
    const [selectedReason, setSelectedReason] = useState("");
    const [customReason, setCustomReason] = useState("");
    const [agreePolicy, setAgreePolicy] = useState(false);
    const [agreeGrace, setAgreeGrace] = useState(false);
    const [errors, setErrors] = useState({});

    const reasonOptions = [
        "No Longer Needed",
        "Mistaken Registration", 
        "Rebranding",
        "Cost Savings",
        "Trademark/Legal Issues",
        "Low Performance",
        "Security/Privacy Concerns",
        "Portfolio Cleanup",
        "Others"
    ];

    const validateForm = () => {
        let newErrors = {};

        if (inputValue.trim().toLowerCase() !== domain?.name?.toLowerCase()) {
            newErrors.domain = ["Domain name does not match."];
        }

        if (!selectedReason) {
            newErrors.reason = ["Please select a reason for deletion."];
        }

        if (selectedReason === "Others") {
            if (!customReason.trim()) {
                newErrors.customReason = ["Please provide a custom reason."];
            } else if (customReason.trim().length < 10) {
                newErrors.customReason = ["Reason must be at least 10 characters."];
            }
        }

        return newErrors;
    };

    const handleSubmit = () => {
        const newErrors = validateForm();

        if (Object.keys(newErrors).length > 0) {
            setErrors(newErrors);
            return;
        }

        setSubmitting(true);
        setErrors({});

        const finalReason = selectedReason === "Others" ? customReason : selectedReason;

        router.post(
            route("domain.delete-request.delete"),
            {
                domainName: domain.name,
                userEmail: domain.domain_email,
                domainId: domain.id,
                createdDate: domain.created_at,
                userID: domain.userId,
                reason: finalReason,
            },
            {
                onSuccess: () => {
                    toast.success("Domain deletion request created successfully.");
                    handleClose();
                    setSubmitting(false);
                    router.visit(route("domain.delete-request.view") + "?statusType=APPROVED");
                },
                onError: (errors) => {
                    setErrors(errors);
                    setSubmitting(false);
                    toast.error("Failed to create deletion request. Please try again.");
                },
            }
        );
    };

    const handleClose = () => {
        setInputValue("");
        setSelectedReason("");
        setCustomReason("");
        setAgreePolicy(false);
        setAgreeGrace(false);
        setErrors({});
        setSubmitting(false);
        onClose();
    };

    if (!domain) return null;

    return (
        <Transition appear show={isOpen} as={Fragment}>
            <Dialog as="div" className="relative z-10" onClose={handleClose}>
                <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <div className="fixed inset-0 bg-black bg-opacity-25" />
                </Transition.Child>

                <div className="fixed inset-0 overflow-y-auto">
                    <div className="flex min-h-full items-center justify-center p-4 text-center">
                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-300"
                            enterFrom="opacity-0 scale-95"
                            enterTo="opacity-100 scale-100"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100 scale-100"
                            leaveTo="opacity-0 scale-95"
                        >
                            <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                                <Dialog.Title
                                    as="h3"
                                    className="text-xl font-semibold leading-6 text-primary mb-4"
                                >
                                    Delete Domain: {domain.name}
                                </Dialog.Title>

                                <div className="mt-2">
                                    <p className="text-sm text-gray-600 mb-6">
                                        To delete {domain.name}, please provide a detailed reason for our review.
                                    </p>

                                    <div className="mb-4">
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Type {domain.name} to confirm <span className="text-red-500">*</span>
                                        </label>
                                        <input
                                            type="text"
                                            value={inputValue}
                                            onChange={(e) => setInputValue(e.target.value)}
                                            placeholder={`Type "${domain.name}" here`}
                                            className={`w-full px-3 py-2 border-2 rounded-md focus:outline-none focus:ring-2 ${
                                                errors.domain
                                                    ? "border-red-500 focus:ring-red-500"
                                                    : "border-primary focus:ring-primary"
                                            }`}
                                        />
                                        {errors.domain && (
                                            <p className="text-sm text-red-600 mt-1">{errors.domain[0]}</p>
                                        )}
                                    </div>

                                    <div className="mb-4">
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Reason for Deletion <span className="text-red-500">*</span>
                                        </label>
                                        <select
                                            value={selectedReason}
                                            onChange={(e) => setSelectedReason(e.target.value)}
                                            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                                                errors.reason
                                                    ? "border-red-500 focus:ring-red-500"
                                                    : "border-gray-300 focus:ring-primary"
                                            }`}
                                        >
                                            <option value="">-- Select a Reason --</option>
                                            {reasonOptions.map((reason) => (
                                                <option key={reason} value={reason}>
                                                    {reason}
                                                </option>
                                            ))}
                                        </select>
                                        {errors.reason && (
                                            <p className="text-sm text-red-600 mt-1">{errors.reason[0]}</p>
                                        )}
                                    </div>

                                    {selectedReason === "Others" && (
                                        <div className="mb-4">
                                            <textarea
                                                value={customReason}
                                                onChange={(e) => setCustomReason(e.target.value)}
                                                placeholder="Please provide your custom reason..."
                                                rows="4"
                                                maxLength={500}
                                                className={`w-full px-3 py-2 border-2 rounded-md focus:outline-none focus:ring-2 resize-none ${
                                                    errors.customReason
                                                        ? "border-red-500 focus:ring-red-500"
                                                        : "border-primary focus:ring-primary"
                                                }`}
                                            />
                                            <div className="text-right text-sm text-gray-500 mt-1">
                                                {customReason.length}/500 characters
                                            </div>
                                            {errors.customReason && (
                                                <p className="text-sm text-red-600 mt-1">{errors.customReason[0]}</p>
                                            )}
                                        </div>
                                    )}
                                </div>

                                <div className="flex justify-end space-x-3">
                                    <SecondaryButton
                                        onClick={handleClose}
                                        disabled={submitting}
                                        className="px-6"
                                    >
                                        CANCEL
                                    </SecondaryButton>
                                    <DangerButton
                                        onClick={handleSubmit}
                                        disabled={submitting}
                                        processing={submitting}
                                        className="px-6"
                                    >
                                        {submitting ? "DELETING..." : "DELETE DOMAIN"}
                                    </DangerButton>
                                </div>
                            </Dialog.Panel>
                        </Transition.Child>
                    </div>
                </div>
            </Dialog>
        </Transition>
    );
}
